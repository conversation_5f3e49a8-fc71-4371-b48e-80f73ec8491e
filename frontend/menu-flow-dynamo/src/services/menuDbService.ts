import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';
import { RestaurantInfo } from '@/contexts/RestaurantContext';

// Menu data interface for creating/updating menus
export interface MenuData {
  name: string;
  description?: string;
  is_active?: boolean;
  restaurant_id?: string;
  start_time?: string;
  end_time?: string;
  days_available?: number[]; // Changed to number[] to match database INTEGER[] type
}

// Complete Menu interface with all properties
export interface Menu extends MenuData {
  id: string;
  created_at: string;
  updated_at: string;
  restaurant_id: string; // Ensure restaurant_id is required
}

/**
 * Fetch all menus for a specific restaurant
 * @param restaurantInfo The restaurant information from context or a simplified object with id and name
 * @returns Array of menus for the restaurant
 */
export const fetchMenus = async (restaurantInfo: { id: string; name?: string }): Promise<Menu[]> => {
  try {
    // Ensure we have a valid restaurant ID
    if (!restaurantInfo || !restaurantInfo.id) {
      console.error('Cannot fetch menus: No valid restaurant ID available', { restaurantInfo });
      return [];
    }
    
    const restaurantId = restaurantInfo.id;
    console.log(`Fetching menus for restaurant: ${restaurantInfo.name} (ID: ${restaurantId})`);
    
    // Debug log to verify the restaurant ID before query
    console.log("Will fetch menus for restaurant ID:", restaurantId, 
                "Looking for menus with this restaurant_id in the database");

    // Fetch menus for this specific restaurant only
    const { data, error } = await supabase
      .from('menus')
      .select('*')
      .eq('restaurant_id', restaurantId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching menus:', error.message, error);
      return [];
    }

    // Detailed logging of the results
    console.log(`Found ${data?.length || 0} menus for restaurant: ${restaurantInfo.name} (ID: ${restaurantId})`);
    console.log("Menus returned:", data);
    
    // If no menus found but we know they exist, this could indicate an RLS issue
    if (!data || data.length === 0) {
      console.warn("No menus found for this restaurant. If menus exist in the database, check:");
      console.warn("1. RLS policies on the menus table");
      console.warn("2. User authentication status");
      console.warn("3. Restaurant ownership in the database");
    }
    
    return data || [];
  } catch (error) {
    console.error('Error fetching menus:', error);
    return []; // Return empty array instead of throwing to prevent UI errors
  }
};

/**
 * Fetch a single menu by ID, ensuring it belongs to the specified restaurant
 * @param menuId The ID of the menu to fetch
 * @param restaurantInfo The restaurant information from context
 * @returns The menu if found and belongs to the restaurant, null otherwise
 */
export const fetchMenuById = async (menuId: string, restaurantInfo: RestaurantInfo): Promise<Menu | null> => {
  try {
    // Ensure we have a valid restaurant ID
    if (!restaurantInfo || !restaurantInfo.id) {
      console.error('Cannot fetch menu: No valid restaurant ID available');
      return null;
    }
    
    // Fetch the menu and ensure it belongs to this restaurant
    const { data, error } = await supabase
      .from('menus')
      .select('*')
      .eq('id', menuId)
      .eq('restaurant_id', restaurantInfo.id) // Ensure menu belongs to this restaurant
      .single();

    if (error) {
      console.error('Error fetching menu by ID:', error.message);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error fetching menu by ID:', error);
    return null;
  }
};

/**
 * Create a new menu for a specific restaurant
 * @param menuData The menu data to create
 * @param restaurantInfo The restaurant information from context
 * @param user The authenticated user
 * @returns The created menu if successful, null otherwise
 */
export const createMenu = async (menuData: MenuData, restaurantInfo: RestaurantInfo, user: User): Promise<Menu | null> => {
  try {
    console.log('🍽️ Starting createMenu function');
    console.log('📊 Menu data received:', menuData);
    console.log('🏪 Restaurant info:', restaurantInfo);
    console.log('👤 User info:', { id: user.id, email: user.email });

    // Ensure we have a valid restaurant ID and user
    if (!restaurantInfo || !restaurantInfo.id) {
      console.error('❌ Cannot create menu: No valid restaurant ID available');
      return null;
    }

    if (!user) {
      console.error('❌ Cannot create menu: No user provided');
      return null;
    }

    // Always use the restaurant ID from the context to ensure data isolation
    const restaurantId = restaurantInfo.id;
    console.log('🎯 Using restaurant ID:', restaurantId);
    
    // Verify the user has access to this business by checking if they own it
    console.log('🔍 Verifying user access to business...');
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id')
      .eq('id', restaurantId)
      .eq('user_id', user.id)
      .single();

    console.log('🏢 Business query result:', { business, businessError });

    if (businessError || !business) {
      console.error('❌ User does not have access to this business:', businessError);
      return null;
    }

    console.log('✅ User access verified for business:', business.id);

    // Check if a menu with this name already exists for this restaurant
    const { data: existingMenus, error: checkError } = await supabase
      .from('menus')
      .select('id, name')
      .eq('restaurant_id', restaurantId)
      .eq('name', menuData.name);

    if (checkError) {
      console.error('Error checking for existing menu:', checkError);
    } else if (existingMenus && existingMenus.length > 0) {
      console.log(`Menu "${menuData.name}" already exists for this restaurant, fetching complete details`);
      
      // Fetch the complete menu details to ensure we have all required properties
      const { data: completeMenu, error: fetchError } = await supabase
        .from('menus')
        .select('*')
        .eq('id', existingMenus[0].id)
        .single();
        
      if (fetchError) {
        console.error('Error fetching complete menu details:', fetchError);
        return null;
      }
      
      return completeMenu as Menu;
    }

    // Prepare the insert data
    const insertData = {
      ...menuData,
      restaurant_id: restaurantId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('Creating menu with data:', insertData);
    console.log('Current user:', user.id);
    console.log('Restaurant ID:', restaurantId);

    // Check current session
    const { data: session } = await supabase.auth.getSession();
    console.log('Current session user ID:', session?.session?.user?.id);
    console.log('Session matches user:', session?.session?.user?.id === user.id);

    const { data, error } = await supabase
      .from('menus')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('Error in Supabase insert operation:', error);
      console.error('Error details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      throw new Error(`Failed to create menu: ${error.message}`);
    }

    if (!data) {
      console.error('No data returned from menu creation');
      throw new Error('Menu creation failed - no data returned from database');
    }

    console.log('Menu created successfully:', data);
    return data as Menu;
  } catch (error) {
    console.error('Error creating menu:', error);
    return null;
  }
};

/**
 * Update an existing menu for a specific restaurant
 * @param menuId The ID of the menu to update
 * @param menuData The updated menu data
 * @param restaurantInfo The restaurant information from context
 * @param user The authenticated user
 * @returns The updated menu if successful, null otherwise
 */
export const updateMenu = async (
  menuId: string, 
  menuData: MenuData, 
  restaurantInfo: RestaurantInfo, 
  user: User
): Promise<Menu | null> => {
  try {
    // Ensure we have a valid restaurant ID and user
    if (!restaurantInfo || !restaurantInfo.id) {
      console.error('Cannot update menu: No valid restaurant ID available');
      return null;
    }
    
    if (!user) {
      console.error('Cannot update menu: No user provided');
      return null;
    }
    
    // First, verify the menu exists and belongs to this specific restaurant
    const { data: existingMenu, error: menuError } = await supabase
      .from('menus')
      .select('*')
      .eq('id', menuId)
      .eq('restaurant_id', restaurantInfo.id) // Ensure menu belongs to this restaurant
      .single();
      
    if (menuError || !existingMenu) {
      console.error('Menu not found or does not belong to this restaurant');
      return null;
    }
    
    // Verify the user has access to this business
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id')
      .eq('id', restaurantInfo.id)
      .eq('user_id', user.id)
      .single();

    if (businessError || !business) {
      console.error('User does not have access to this business');
      return null;
    }
    
    // Prevent changing the restaurant_id to maintain data isolation
    const safeMenuData = { ...menuData };
    delete safeMenuData.restaurant_id;
    
    // Perform the update
    const { data, error } = await supabase
      .from('menus')
      .update({
        ...safeMenuData,
        updated_at: new Date().toISOString()
      })
      .eq('id', menuId)
      .eq('restaurant_id', restaurantInfo.id) // Extra safety check
      .select()
      .single();

    if (error) {
      console.error('Error updating menu:', error.message);
      throw new Error(`Failed to update menu: ${error.message}`);
    }

    if (!data) {
      console.error('No data returned from menu update');
      throw new Error('Menu update failed - no data returned from database');
    }

    console.log('Menu updated successfully:', data);
    return data as Menu;
  } catch (error) {
    console.error('Error updating menu:', error);
    return null;
  }
};

// Delete a menu
export const deleteMenu = async (menuId: string) => {
  try {
    const { error } = await supabase
      .from('menus')
      .delete()
      .eq('id', menuId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting menu:', error);
    throw error;
  }
};
