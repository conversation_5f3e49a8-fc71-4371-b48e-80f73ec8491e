/**
 * MENU CREATION TEST SCRIPT
 * SME Analytica Restaurant System - Menu Management
 * 
 * This script tests the menu creation functionality to ensure:
 * 1. Menu creation works properly
 * 2. UI updates after menu creation
 * 3. Database operations are successful
 * 4. React Query cache invalidation works
 */

console.log('🍽️ Starting Menu Creation Test Suite');
console.log('📅 Test Time:', new Date().toLocaleString());

// Test Configuration
const TEST_CONFIG = {
  testTimeout: 15000,
  retryAttempts: 3,
  waitBetweenActions: 1000
};

// Test Results Storage
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// Utility Functions
function logTest(testName, status, details = '') {
  const result = { testName, status, details, timestamp: new Date().toISOString() };
  testResults.tests.push(result);
  
  if (status === 'PASS') {
    testResults.passed++;
    console.log(`✅ ${testName}: PASSED ${details}`);
  } else if (status === 'PARTIAL') {
    console.log(`⚠️ ${testName}: PARTIAL ${details}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: FAILED ${details}`);
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function generateTestMenuName() {
  const timestamp = Date.now();
  return `Test Menu ${timestamp}`;
}

// TEST 1: Check if we're on the Menus Management page
async function testMenuManagementPageAccess() {
  console.log('\n🔍 TEST 1: Menu Management Page Access');
  
  try {
    const isMenuManagementPage = window.location.pathname.includes('/menus-management') || 
                                 window.location.pathname.includes('/admin/menus');
    
    if (!isMenuManagementPage) {
      logTest('Menu Management Page Access', 'FAIL', 'Not on menus management page');
      return false;
    }
    
    // Check for key elements
    const addMenuButton = document.querySelector('button[class*="add"], button:contains("Add Menu")') ||
                         Array.from(document.querySelectorAll('button')).find(btn => 
                           btn.textContent.toLowerCase().includes('add menu'));
    
    const menusTable = document.querySelector('table, [class*="table"]');
    
    if (addMenuButton) {
      logTest('Add Menu Button', 'PASS', 'Add Menu button found');
    } else {
      logTest('Add Menu Button', 'FAIL', 'Add Menu button not found');
    }
    
    if (menusTable) {
      logTest('Menus Table', 'PASS', 'Menus table found');
    } else {
      logTest('Menus Table', 'PARTIAL', 'Menus table not clearly identified');
    }
    
    logTest('Menu Management Page Access', 'PASS', 'Successfully accessed menus management page');
    return true;
    
  } catch (error) {
    logTest('Menu Management Page Access', 'FAIL', `Error: ${error.message}`);
    return false;
  }
}

// TEST 2: Test Menu Creation Process
async function testMenuCreation() {
  console.log('\n🍽️ TEST 2: Menu Creation Process');
  
  try {
    // Find and click the Add Menu button
    const addMenuButton = document.querySelector('button[class*="add"], button:contains("Add Menu")') ||
                         Array.from(document.querySelectorAll('button')).find(btn => 
                           btn.textContent.toLowerCase().includes('add menu'));
    
    if (!addMenuButton) {
      logTest('Menu Creation - Button Click', 'FAIL', 'Add Menu button not found');
      return false;
    }
    
    // Count existing menus before creation
    const existingMenuRows = document.querySelectorAll('table tbody tr, [class*="menu-row"]');
    const initialMenuCount = existingMenuRows.length;
    console.log(`Initial menu count: ${initialMenuCount}`);
    
    // Click the Add Menu button
    addMenuButton.click();
    await sleep(1000);
    
    // Check if dialog/form opened
    const menuDialog = document.querySelector('[role="dialog"], .dialog, [class*="dialog"]');
    const menuForm = document.querySelector('form, [class*="form"]');
    
    if (!menuDialog && !menuForm) {
      logTest('Menu Creation - Dialog Open', 'FAIL', 'Menu creation dialog did not open');
      return false;
    }
    
    logTest('Menu Creation - Dialog Open', 'PASS', 'Menu creation dialog opened');
    
    // Fill in the form
    const testMenuName = generateTestMenuName();
    console.log(`Creating test menu: ${testMenuName}`);
    
    const nameInput = document.querySelector('input[name="name"], input[id="name"], input[placeholder*="name"]');
    const descriptionInput = document.querySelector('textarea[name="description"], textarea[id="description"], input[name="description"]');
    
    if (nameInput) {
      nameInput.value = testMenuName;
      nameInput.dispatchEvent(new Event('input', { bubbles: true }));
      nameInput.dispatchEvent(new Event('change', { bubbles: true }));
      logTest('Menu Creation - Name Input', 'PASS', `Set menu name to: ${testMenuName}`);
    } else {
      logTest('Menu Creation - Name Input', 'FAIL', 'Name input field not found');
      return false;
    }
    
    if (descriptionInput) {
      const testDescription = `Test menu created at ${new Date().toLocaleString()}`;
      descriptionInput.value = testDescription;
      descriptionInput.dispatchEvent(new Event('input', { bubbles: true }));
      descriptionInput.dispatchEvent(new Event('change', { bubbles: true }));
      logTest('Menu Creation - Description Input', 'PASS', 'Set menu description');
    }
    
    // Find and click submit button
    const submitButton = document.querySelector('button[type="submit"], button:contains("Add Menu"), button:contains("Create"), button:contains("Save")') ||
                        Array.from(document.querySelectorAll('button')).find(btn => 
                          btn.textContent.toLowerCase().includes('add menu') || 
                          btn.textContent.toLowerCase().includes('create') ||
                          btn.textContent.toLowerCase().includes('save'));
    
    if (!submitButton) {
      logTest('Menu Creation - Submit Button', 'FAIL', 'Submit button not found');
      return false;
    }
    
    // Click submit and wait for response
    console.log('Submitting menu creation form...');
    submitButton.click();
    
    // Wait for form submission and UI update
    await sleep(3000);
    
    // Check for success indicators
    const successToast = document.querySelector('[class*="toast"], [class*="notification"], [class*="alert"]');
    const hasSuccessText = document.body.textContent.includes('Menu created') || 
                          document.body.textContent.includes('successfully') ||
                          document.body.textContent.includes('Success');
    
    if (successToast || hasSuccessText) {
      logTest('Menu Creation - Success Feedback', 'PASS', 'Success message displayed');
    } else {
      logTest('Menu Creation - Success Feedback', 'PARTIAL', 'No clear success message found');
    }
    
    // Check if dialog closed
    const dialogStillOpen = document.querySelector('[role="dialog"], .dialog, [class*="dialog"]');
    if (!dialogStillOpen) {
      logTest('Menu Creation - Dialog Close', 'PASS', 'Dialog closed after submission');
    } else {
      logTest('Menu Creation - Dialog Close', 'PARTIAL', 'Dialog may still be open');
    }
    
    // Wait a bit more for UI updates
    await sleep(2000);
    
    // Check if new menu appears in the list
    const updatedMenuRows = document.querySelectorAll('table tbody tr, [class*="menu-row"]');
    const finalMenuCount = updatedMenuRows.length;
    
    console.log(`Final menu count: ${finalMenuCount}`);
    
    if (finalMenuCount > initialMenuCount) {
      logTest('Menu Creation - UI Update', 'PASS', `Menu count increased from ${initialMenuCount} to ${finalMenuCount}`);
    } else {
      logTest('Menu Creation - UI Update', 'FAIL', `Menu count did not increase (${initialMenuCount} -> ${finalMenuCount})`);
    }
    
    // Check if the new menu name appears in the UI
    const menuNameInUI = document.body.textContent.includes(testMenuName);
    if (menuNameInUI) {
      logTest('Menu Creation - Name in UI', 'PASS', `New menu "${testMenuName}" appears in UI`);
    } else {
      logTest('Menu Creation - Name in UI', 'FAIL', `New menu "${testMenuName}" not found in UI`);
    }
    
    return true;
    
  } catch (error) {
    logTest('Menu Creation Process', 'FAIL', `Error: ${error.message}`);
    return false;
  }
}

// TEST 3: Check React Query Cache Status
async function testReactQueryCache() {
  console.log('\n🔄 TEST 3: React Query Cache Status');
  
  try {
    // Check if React Query is available
    const reactQuery = window.ReactQuery || window.__REACT_QUERY_DEVTOOLS__;
    
    if (reactQuery) {
      logTest('React Query Detection', 'PASS', 'React Query detected');
    } else {
      logTest('React Query Detection', 'PARTIAL', 'React Query not directly accessible');
    }
    
    // Check for query client in the global scope
    const queryClient = window.queryClient || window.__queryClient;
    
    if (queryClient) {
      logTest('Query Client Access', 'PASS', 'Query client accessible');
    } else {
      logTest('Query Client Access', 'PARTIAL', 'Query client not directly accessible');
    }
    
    // Check console for query-related logs
    const hasQueryLogs = window.console._logs?.some(log => 
      log.includes('query') || log.includes('cache') || log.includes('refetch')
    );
    
    if (hasQueryLogs) {
      logTest('Query Cache Activity', 'PASS', 'Query cache activity detected in logs');
    } else {
      logTest('Query Cache Activity', 'PARTIAL', 'No obvious query cache activity in logs');
    }
    
  } catch (error) {
    logTest('React Query Cache Status', 'FAIL', `Error: ${error.message}`);
  }
}

// Main Test Runner
async function runMenuCreationTests() {
  console.log('\n🎯 Running Menu Creation Test Suite...\n');
  
  const pageAccessSuccess = await testMenuManagementPageAccess();
  
  if (pageAccessSuccess) {
    await testMenuCreation();
  } else {
    console.log('⚠️ Skipping menu creation test - not on correct page');
  }
  
  await testReactQueryCache();
  
  // Generate Test Report
  console.log('\n📊 MENU CREATION TEST REPORT');
  console.log('='.repeat(50));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📊 Total: ${testResults.tests.length}`);
  console.log(`🎯 Success Rate: ${((testResults.passed / testResults.tests.length) * 100).toFixed(1)}%`);
  
  // Detailed Results
  console.log('\n📋 DETAILED RESULTS:');
  testResults.tests.forEach(test => {
    const icon = test.status === 'PASS' ? '✅' : test.status === 'PARTIAL' ? '⚠️' : '❌';
    console.log(`${icon} ${test.testName}: ${test.status} ${test.details}`);
  });
  
  return testResults;
}

// Auto-run tests when script loads
if (typeof window !== 'undefined') {
  // Wait for page to be fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(runMenuCreationTests, 2000);
    });
  } else {
    setTimeout(runMenuCreationTests, 2000);
  }
}

// Export for manual testing
window.runMenuCreationTests = runMenuCreationTests;
window.testMenuCreation = testMenuCreation;

console.log('📝 Menu creation test suite loaded. Run window.runMenuCreationTests() to execute manually.');
